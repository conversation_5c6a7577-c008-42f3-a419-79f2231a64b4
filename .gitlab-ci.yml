variables:
  APP_NAME: "generative-card-service"
  APP_VERSION: "v1.0"

stages:
  - package
  - build
  - deploy_qa
  - done


maven_package:
  stage: package
  image: maven:3.8-openjdk-17
  tags:
    - bigdata
  only:
    - develop
    - master
  before_script:
    - echo "===============  开始编译打包任务  ==============="
    - rm -rf ${ARTIFACTS_PATH} && mkdir ${ARTIFACTS_PATH}
  script:
    - mvn --settings /home/<USER>/.m2/settings.xml clean package -DskipTests
  after_script:
    - cp Dockerfile ${ARTIFACTS_PATH}
    - cp target/${APP_NAME}.jar ${ARTIFACTS_PATH}



# 定义任务：构建镜像
docker_build:
  stage: build
  image: docker:20.10.5
  tags:
    - bigdata
  only:
    - develop
    - master
  before_script:
    - echo "=============== 开始构建docker镜像任务  ==============="
  script:
    - cp ${ARTIFACTS_PATH}/${APP_NAME}.jar ./
    - echo "===================  当前目录  ======================="
    - pwd
    - docker build -t ${BT_ARTIFACT_IMAGE} .
    - docker push ${BT_ARTIFACT_IMAGE}


k8s_deploy_qa:
  image: bigdata-registry-bt.beantechyun.cn:5000/kubectl:alpine-v1.28
  stage: deploy_qa
  variables:
    BASE64_KUBE_CONFIG: ${QA_KUBE_CONFIG}
    ENV: "qa"
    REPLICAS: 1
    RESOURCES_CPU: 100m
    RESOURCES_MEMORY: 200Mi
    RESOURCES_LIMIT_CPU: 1000m
    RESOURCES_LIMIT_MEMORY: 2G
    K8S_API_VERSION: apps/v1
    SKYWALKING_IMAGE: bigdata-registry-bt.beantechyun.cn/skywalking-java-agent:9.4.0-alpine
  tags:
    - bigdata
  when: always
  before_script:
    - init-kube-config
  script:
    - echo "===============  开始发布Kubernetes Deployment任务  ==============="
    - IMAGE=`echo $BT_ARTIFACT_IMAGE | sed 's#\/#\\\/#g'`
    - K8S_API_VERSION=`echo $K8S_API_VERSION | sed 's#\/#\\\/#g'`
    - SKYWALKING_IMAGE=`echo $SKYWALKING_IMAGE | sed 's#\/#\\\/#g'`
    - sed -i "s/DOCKER_IMAGE/${IMAGE}/g" deploy.yaml
    - sed -i "s/ACTIVE_PROFILE/${ENV}/g"  ./deploy.yaml
    - sed -i "s/REPLICASNUM/${REPLICAS}/g"  ./deploy.yaml
    - sed -i "s/RESOURCES_CPU/${RESOURCES_CPU}/g"  ./deploy.yaml
    - sed -i "s/RESOURCES_MEMORY/${RESOURCES_MEMORY}/g"  ./deploy.yaml
    - sed -i "s/RESOURCES_LIMIT_CPU/${RESOURCES_LIMIT_CPU}/g"  ./deploy.yaml
    - sed -i "s/RESOURCES_LIMIT_MEMORY/${RESOURCES_LIMIT_MEMORY}/g"  ./deploy.yaml
    - sed -i "s/K8S_API_VERSION/${K8S_API_VERSION}/g"  ./deploy.yaml
    - sed -i "s/SKYWALKING_IMAGE/${SKYWALKING_IMAGE}/g"  ./deploy.yaml
    - cat deploy.yaml
    - kubectl apply -f deploy.yaml --record=true


builds_expires_clean:
  stage: done
  variables:
    EXPIRES_DAYS: 30
  tags:
    - bigdata
  when: always
  allow_failure: true
  before_script:
    - echo "===============  开始清理过期（${EXPIRES_DAYS}天之前）构建目录任务  ==============="
  script:
    - echo "项目目录： ${CI_PROJECT_DIR}"
    - echo "需要清理目录列表："
    - find ${CI_PROJECT_DIR} -mtime +${EXPIRES_DAYS} -name "*"
    - echo "开始清理..."
    - find ${CI_PROJECT_DIR} -mtime +${EXPIRES_DAYS} -name "*" -exec rm -rf {} \;


image_expires_clean:
  stage: done
  image: docker:20.10.5
  variables:
    KEEP_IMAGE_COUNT: 10
  tags:
    - bigdata
  when: always
  allow_failure: true
  before_script:
    - echo "===============  开始清理过期（保留最近的${KEEP_IMAGE_COUNT}个镜像）docker镜像任务  ==============="
  script:
    - echo "应用${APP_NAME}的镜像列表："
    - docker images | grep ${REPOSITORY_PREFIX} | grep ${APP_NAME}
    - echo $((COUNT=KEEP_IMAGE_COUNT +1))
    - echo "需要清理的${APP_NAME}镜像列表："
    - docker images | grep ${REPOSITORY_PREFIX}  | grep ${APP_NAME} | tail -n +${COUNT}
    - echo "开始清理..."
    - docker images | grep ${REPOSITORY_PREFIX}  | grep ${APP_NAME} | tail -n +${COUNT} | awk '{print $1":"$2}' | xargs -r -t docker rmi