{"serverUrl": "http://[域名]/[后缀]", "outPath": "src/main/resources/doc", "isStrict": false, "allInOne": true, "createDebugPage": false, "style": "xt256", "projectName": "生成式卡片服务API文档", "showAuthor": true, "allInOneDocFileName": "index.html", "requestExample": true, "responseExample": true, "requestFieldToUnderline": true, "responseFieldToUnderline": true, "swagger": true, "requestHeaders": [{"name": "Content-Type", "type": "string", "desc": "请求内容类型", "value": "application/json", "required": false, "since": "-"}], "requestParams": [{"name": "version", "type": "string", "desc": "API版本号", "paramIn": "query", "value": "v1", "required": false, "since": "-"}], "responseBodyAdvice": {"className": "com.gwm.ailab.service.common.ResponseResult"}, "groups": [{"name": "生成式卡片接口", "apis": "com.gwm.ailab.service.controller.GenerativeCardController"}, {"name": "卡片模板管理接口", "apis": "com.gwm.ailab.service.controller.TemplateController"}], "packageFilters": "", "packageInclude": "com.gwm.ailab.service.controller.*", "packageExclude": "", "revisionLogs": [{"version": "1.0.0", "revisionTime": "2024-01-01 10:00:00", "status": "创建", "author": "AI Lab", "remarks": "初始版本，包含生成式卡片和模板管理接口"}], "customResponseFields": [{"name": "code", "desc": "响应状态码", "ownerClassName": "com.gwm.ailab.service.common.ResponseResult", "ignore": false, "value": "200"}, {"name": "message", "desc": "响应消息", "ownerClassName": "com.gwm.ailab.service.common.ResponseResult", "ignore": false, "value": "Success"}, {"name": "data", "desc": "响应数据", "ownerClassName": "com.gwm.ailab.service.common.ResponseResult", "ignore": false, "value": "实际业务数据"}, {"name": "timestamp", "desc": "响应时间戳", "ownerClassName": "com.gwm.ailab.service.common.ResponseResult", "ignore": false, "value": "2024-01-01T10:00:00"}]}