package com.gwm.ailab.service.controller;

import com.alibaba.fastjson2.JSONObject;
import com.gwm.ailab.service.common.ResponseResult;
import com.gwm.ailab.service.service.CardTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 模板控制器
 *
 * <AUTHOR> Lab GW00295473
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/template")
@Validated
@Tag(name = "服务卡片模板管理", description = "卡片模板相关的API接口")
@RequiredArgsConstructor
public class TemplateController {

    private final CardTemplateService cardTemplateService;

    /**
     * 卡片模板同步接口
     *
     * @param json 模板同步请求参数
     * @return ResponseResult<Void> 同步结果，成功返回200状态码
     * @throws Exception 当同步过程中发生错误时抛出异常
     * <AUTHOR> Lab GW00295473
     * @since 1.0.0
     */
    @PostMapping("/manual/sync")
    @Operation(
            summary = "卡片模板手动同步接口",
            description = "同步卡片模板到Redis缓存中"
    )
    @ApiResponse(
            responseCode = "200",
            description = "模板同步成功。模板已成功保存到Redis缓存中，" +
                          "返回标准的成功响应格式。同步的模板将在下次卡片生成时生效。",
            content = @Content(mediaType = "application/json")
    )
    @ApiResponse(
            responseCode = "400",
            description = "请求参数错误。可能的错误情况包括：" +
                          "- 缺少必填字段 code 或 content" +
                          "- 模板编码格式不正确" +
                          "- 模板内容格式不符合要求",
            content = @Content(mediaType = "application/json")
    )
    @ApiResponse(
            responseCode = "500",
            description = "服务器内部错误。可能的错误情况包括：" +
                          "- Redis连接失败" +
                          "- 模板内容解析错误" +
                          "- 系统异常",
            content = @Content(mediaType = "application/json")
    )
    public ResponseResult<Void> templateSync(@RequestBody JSONObject json) {
        cardTemplateService.syncTemplateToRedis(json);
        return ResponseResult.success();
    }


}
